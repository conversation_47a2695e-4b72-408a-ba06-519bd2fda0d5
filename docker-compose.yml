services:
  # MongoDB 数据库
  mongodb:
    image: mongo:7.0
    container_name: findu-mongodb
    restart: always
    env_file: .env # 加载环境变量文件
    volumes:
      - mongodb_data:/data/db
    networks:
      - findu-network
    # 定义健康检查
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 后端服务
  backend:
    build:
      context: ./findu-backend
      dockerfile: Dockerfile
    container_name: findu-backend
    restart: always
    env_file: .env # 加载环境变量文件
    # 移除端口映射，由 Nginx 反向代理
    # ports:
    #   - "8000:8000"
    volumes:
      - backend_storage:/app/static
    networks:
      - findu-network
    # 定义健康检查，确保服务可用
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"] # 根据你的后端API路径调整
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    depends_on:
      mongodb:
        condition: service_healthy

  # 前端服务
  frontend:
    build:
      context: ./findu-frontend
      dockerfile: Dockerfile
    container_name: findu-frontend
    restart: always
    environment:
      # 在容器内部，使用后端服务的名称作为域名
      - NEXT_PUBLIC_API_URL=http://backend:8000/api
      - NEXT_PUBLIC_APP_NAME=AI需求生成器
      - NEXT_PUBLIC_DEFAULT_LOCALE=en
    # 移除端口映射，由 Nginx 反向代理
    # ports:
    #   - "3000:3000"
    networks:
      - findu-network
    depends_on:
      backend:
        condition: service_healthy
  
  # Nginx 反向代理
  nginx:
    image: nginx:1.25.3-alpine
    container_name: findu-nginx
    restart: always
    ports:
      - "80:80" # 映射到主机的 80 端口
      - "443:443" # 映射到主机的 443 端口（用于 HTTPS）
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro # 映射 Nginx 配置文件
      # 如果需要，这里可以映射 SSL 证书
    networks:
      - findu-network
    depends_on:
      - frontend
      - backend

volumes:
  mongodb_data:
  backend_storage:

networks:
  findu-network:
    driver: bridge