<<<<<<< HEAD
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=70
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    api: API tests
    db: Database tests
    ai: AI service tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
=======
[pytest]
minversion = 7.0
addopts = -q
pythonpath = .

>>>>>>> gellar
