# FindU Backend Dockerfile

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set non-interactive mode for debconf
ENV DEBIAN_FRONTEND=noninteractive

# Use a domestic apt mirror for system packages
RUN rm -rf /etc/apt/sources.list.d/ && \
    echo "deb http://mirrors.aliyun.com/debian/ trixie main" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security/ trixie-security main" >> /etc/apt/sources.list

# Update apt and clear cache
RUN apt-get update && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies using a domestic PyPI mirror
RUN pip install --no-cache-dir -r requirements.txt --index-url https://pypi.tuna.tsinghua.edu.cn/simple

# Copy application code
COPY . .

# Create static files directory
RUN mkdir -p static/demands

# Expose port
EXPOSE 8000

# Start command
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
