# FindU Backend Environment Variables
# 复制此文件为 .env 并填入实际值

# 数据库配置
MONGODB_URL=mongodb://localhost:27017
# 数据库功能开关（MVP版本默认关闭）
# 设置为 true 启用数据持久化功能
ENABLE_DATABASE=false

# 应用配置
FRONTEND_URL=http://localhost:3000
JWT_SECRET=your_jwt_secret_key_here

# 文件存储配置
STORAGE_PATH=static/demands
STORAGE_URL=http://localhost:8000

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 通义千问AI参数
AI_PROVIDER=qwen
AI_API_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
AI_API_KEY=your_api_key_here
AI_MODEL=qwen-turbo

# AI服务超时和重试配置
AI_TIMEOUT=60
AI_MAX_RETRIES=2
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=4000
