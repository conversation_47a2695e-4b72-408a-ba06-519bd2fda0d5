version: '3.8'

services:
  # MongoDB Production Database
  mongodb-prod:
    image: mongo:5.0
    container_name: findu-prod-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
    volumes:
      - mongodb_prod_data:/data/db
      - ./mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - findu-prod-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis for Caching (Optional)
  redis-prod:
    image: redis:7-alpine
    container_name: findu-prod-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_prod_data:/data
    networks:
      - findu-prod-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend Production Service
  backend-prod:
    image: ${DOCKER_REGISTRY}/findu-backend:${IMAGE_TAG:-latest}
    container_name: findu-prod-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME}:${MONGO_ROOT_PASSWORD}@mongodb-prod:27017/${MONGO_DATABASE}?authSource=admin
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis-prod:6379/0
      - X=${AI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - FRONTEND_URL=${FRONTEND_URL}
      - STATIC_FILES_PATH=/app/static
      - LOG_LEVEL=INFO
    depends_on:
      mongodb-prod:
        condition: service_healthy
      redis-prod:
        condition: service_healthy
    networks:
      - findu-prod-network
    volumes:
      - backend_static_files:/app/static
      - backend_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Frontend Production Service
  frontend-prod:
    image: ${DOCKER_REGISTRY}/findu-frontend:${IMAGE_TAG:-latest}
    container_name: findu-prod-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${BACKEND_URL}
    depends_on:
      backend-prod:
        condition: service_healthy
    networks:
      - findu-prod-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Nginx Reverse Proxy
  nginx-prod:
    image: nginx:alpine
    container_name: findu-prod-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - backend_static_files:/var/www/static
    depends_on:
      - frontend-prod
      - backend-prod
    networks:
      - findu-prod-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Monitoring - Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: findu-prod-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - findu-prod-network
    profiles:
      - monitoring

  # Monitoring - Grafana (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: findu-prod-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - findu-prod-network
    profiles:
      - monitoring

  # Log Aggregation - ELK Stack (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: findu-prod-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - findu-prod-network
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: findu-prod-logstash
    restart: unless-stopped
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch
    networks:
      - findu-prod-network
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: findu-prod-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - findu-prod-network
    profiles:
      - logging

networks:
  findu-prod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mongodb_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  backend_static_files:
    driver: local
  backend_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
